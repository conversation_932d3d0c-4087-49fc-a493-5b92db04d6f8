import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'
import { useAppStore } from '../store'

interface FileTreeNode {
  type: 'folder' | 'file'
  name: string
  path: string
  isExpanded?: boolean
  isSelected?: boolean
  children?: FileTreeNode[]
  fileCount?: number
  icon: string
  color: string
}

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}

const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const { artifactsVisible } = useAppStore()

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])
  
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'master', // Default to master mode as shown in design
    showArtifacts: false,
    artifactsExpanded: false
  })
  
  const [selectedFile, setSelectedFile] = useState<string | null>('master.md')
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root', 'components']))
  const [fileTree] = useState<FileTreeNode[]>([
    {
      type: 'folder',
      name: 'project-alpha',
      path: '/project-alpha',
      isExpanded: true,
      fileCount: 3,
      icon: 'fa-folder',
      color: 'text-supplement2',
      children: [
        {
          type: 'file',
          name: 'master.md',
          path: '/project-alpha/master.md',
          isSelected: true,
          icon: 'fa-file-lines',
          color: 'text-primary'
        },
        {
          type: 'folder',
          name: 'design',
          path: '/project-alpha/design',
          isExpanded: false,
          fileCount: 2,
          icon: 'fa-folder',
          color: 'text-supplement2',
          children: []
        },
        {
          type: 'folder',
          name: 'components',
          path: '/project-alpha/components',
          isExpanded: true,
          fileCount: 2,
          icon: 'fa-folder',
          color: 'text-supplement2',
          children: [
            {
              type: 'file',
              name: 'buttons.md',
              path: '/project-alpha/components/buttons.md',
              icon: 'fa-file-lines',
              color: 'text-secondary'
            },
            {
              type: 'file',
              name: 'forms.md',
              path: '/project-alpha/components/forms.md',
              icon: 'fa-file-lines',
              color: 'text-gray-400'
            }
          ]
        },
        {
          type: 'file',
          name: 'tokens.json',
          path: '/project-alpha/tokens.json',
          icon: 'fa-file-code',
          color: 'text-supplement2'
        }
      ]
    }
  ])

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })
  }

  const selectFile = (path: string) => {
    setSelectedFile(path)
  }

  const handleModeChange = (mode: 'explorer' | 'master') => {
    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))

    // Auto-select master.md when switching to master mode
    if (mode === 'master') {
      setSelectedFile('master.md')
    }
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path)
            } else {
              selectFile(node.path)
            }
          }}
        >
          {node.type === 'folder' && (
            <i className={`text-gray-400 text-xs w-3 ${isExpanded ? 'fa-chevron-down' : 'fa-chevron-right'}`}></i>
          )}
          {node.type === 'file' && <div className="w-3"></div>}
          
          <i className={`${node.icon} text-sm ${node.color}`}></i>
          
          <span className={`text-sm ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          
          <div className="ml-auto">
            {node.fileCount && (
              <span className={`w-5 h-5 ${node.color === 'text-secondary' ? 'bg-secondary/20 text-secondary' : 'bg-supplement2/20 text-supplement2'} text-xs rounded-full flex items-center justify-center font-medium`}>
                {node.fileCount}
              </span>
            )}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Main Files Content */}
      <div className="flex-1 flex bg-gray-900">
        
        {/* Left Column - File Tree (20%) */}
        <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
          
          {/* File Tree Header */}
          <div className="p-4 border-b border-tertiary/50">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-supplement1 text-sm">Files in Vault</h3>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <i className="fa-plus text-gray-400 text-xs"></i>
                <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  Add File
                </div>
              </button>
            </div>
          </div>

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <i className="fa-sitemap text-sm"></i>
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button 
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <i className="fa-lightbulb text-sm"></i>
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree */}
          <div className="flex-1 overflow-y-auto p-2">
            {fileTree.map(node => renderFileTreeNode(node))}
          </div>
        </div>
        
        {/* Right Column Content */}
        {viewMode.currentMode === 'master' ? (
          <MasterMode />
        ) : (
          <ExplorerMode />
        )}
      </div>
    </div>
  )
}

// Master Mode Component
const MasterMode: React.FC = () => {
  return (
    <div className="flex-1 flex">
      {/* Master Document Preview */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="markdown-content">
          <h1 className="text-supplement1 text-xl font-semibold mb-4">Project Alpha Design System</h1>
          <p className="text-gray-400 mb-6">A comprehensive design system for modern web applications, built with accessibility and scalability in mind.</p>
          
          <h2 className="text-supplement1 text-lg font-medium mb-3 mt-6">Overview</h2>
          <p className="text-gray-400 mb-4">This design system provides a unified set of components, tokens, and guidelines to ensure consistency across all product interfaces. It includes everything from basic UI elements to complex interaction patterns.</p>
          
          <h3 className="text-supplement2 text-base font-medium mb-2 mt-4">Key Features</h3>
          <ul className="text-gray-400 ml-6 mb-4 space-y-1">
            <li>Comprehensive component library with 50+ components</li>
            <li>Design tokens for colors, typography, spacing, and shadows</li>
            <li>Accessibility guidelines and WCAG 2.1 AA compliance</li>
            <li>Dark and light theme support</li>
            <li>Responsive design patterns</li>
            <li>Interactive documentation and examples</li>
          </ul>
          
          <h2 className="text-supplement1 text-lg font-medium mb-3 mt-6">Getting Started</h2>
          <p className="text-gray-400 mb-4">To begin using the design system, install the package and import the necessary components:</p>
          
          <p className="mb-4">
            <code className="bg-gray-700 text-primary px-2 py-1 rounded text-sm">npm install @company/design-system</code>
          </p>
          
          <h3 className="text-supplement2 text-base font-medium mb-2 mt-4">Basic Usage</h3>
          <p className="text-gray-400 mb-4">Import components as needed in your application. Each component comes with full TypeScript support and comprehensive documentation.</p>
        </div>
      </div>
      
      {/* Artifacts Sidebar will be rendered by ArtifactsSidebar component */}
    </div>
  )
}

// Explorer Mode Component
const ExplorerMode: React.FC = () => {
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid')

  const mockFiles = [
    {
      id: '1',
      name: 'design-spec.pdf',
      type: 'pdf',
      size: '2.4 MB',
      modified: '2h ago',
      icon: 'fa-file-pdf',
      color: 'text-primary',
      bgColor: 'bg-primary/20'
    },
    {
      id: '2',
      name: 'requirements.docx',
      type: 'Word',
      size: '1.8 MB',
      modified: '1d ago',
      icon: 'fa-file-word',
      color: 'text-secondary',
      bgColor: 'bg-secondary/20'
    },
    {
      id: '3',
      name: 'notes.md',
      type: 'Markdown',
      size: '12 KB',
      modified: '3h ago',
      icon: 'fa-file-lines',
      color: 'text-supplement2',
      bgColor: 'bg-supplement2/20'
    },
    {
      id: '4',
      name: 'data-analysis.xlsx',
      type: 'Excel',
      size: '856 KB',
      modified: '5h ago',
      icon: 'fa-file-excel',
      color: 'text-green-400',
      bgColor: 'bg-green-500/20'
    },
    {
      id: '5',
      name: 'assets',
      type: 'folder',
      size: '12 items',
      modified: '1d ago',
      icon: 'fa-folder',
      color: 'text-supplement2',
      bgColor: 'bg-supplement2/20'
    },
    {
      id: '6',
      name: 'mockup.png',
      type: 'PNG',
      size: '3.2 MB',
      modified: '6h ago',
      icon: 'fa-file-image',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/20'
    }
  ]

  return (
    <div className="flex-1 flex flex-col bg-gray-900">

      {/* Breadcrumb Navigation */}
      <div className="p-4 border-b border-tertiary/50 bg-gray-800/50">
        <div className="flex items-center gap-2">
          <button className="p-1 hover:bg-gray-700 rounded transition-colors">
            <i className="fa-chevron-left text-gray-400 text-sm"></i>
          </button>
          <button className="p-1 hover:bg-gray-700 rounded transition-colors">
            <i className="fa-chevron-right text-gray-400 text-sm"></i>
          </button>
          <div className="flex items-center gap-1 ml-2">
            <i className="fa-folder text-supplement2 text-sm"></i>
            <span className="text-sm text-supplement1">project-alpha</span>
            <i className="fa-chevron-right text-gray-400 text-xs"></i>
            <span className="text-sm text-gray-400">documents</span>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <button
              onClick={() => setViewType('grid')}
              className={`p-2 hover:bg-gray-700 rounded transition-colors ${viewType === 'grid' ? 'text-primary' : 'text-gray-400'}`}
            >
              <i className="fa-th text-sm"></i>
            </button>
            <button
              onClick={() => setViewType('list')}
              className={`p-2 hover:bg-gray-700 rounded transition-colors ${viewType === 'list' ? 'text-primary' : 'text-gray-400'}`}
            >
              <i className="fa-list text-sm"></i>
            </button>
          </div>
        </div>
      </div>

      {/* File Grid/List View */}
      <div className="flex-1 overflow-y-auto p-4">
        {viewType === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {mockFiles.map((file) => (
              <div
                key={file.id}
                className="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30"
                onClick={() => {
                  if (file.type !== 'folder') {
                    // Open artifacts for supported file types
                    console.log('Opening file:', file.name)
                  }
                }}
              >
                <div className="flex flex-col items-center text-center">
                  <div className={`w-12 h-12 ${file.bgColor} rounded-lg flex items-center justify-center mb-3`}>
                    <i className={`${file.icon} ${file.color} text-xl`}></i>
                  </div>
                  <h4 className="text-sm font-medium text-supplement1 mb-1 truncate w-full">{file.name}</h4>
                  <p className="text-xs text-gray-400">{file.size} • {file.type}</p>
                  <p className="text-xs text-gray-500 mt-1">Modified {file.modified}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {mockFiles.map((file) => (
              <div
                key={file.id}
                className="file-item p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30"
                onClick={() => {
                  if (file.type !== 'folder') {
                    // Open artifacts for supported file types
                    console.log('Opening file:', file.name)
                  }
                }}
              >
                <div className="flex items-center gap-3">
                  <div className={`w-8 h-8 ${file.bgColor} rounded flex items-center justify-center`}>
                    <i className={`${file.icon} ${file.color} text-sm`}></i>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-supplement1 truncate">{file.name}</h4>
                    <p className="text-xs text-gray-400">{file.size} • {file.type}</p>
                  </div>
                  <div className="text-xs text-gray-500">
                    Modified {file.modified}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="p-3 border-t border-tertiary/50 bg-gray-800/50">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <span>{mockFiles.length} items</span>
          <span>Total: {mockFiles.length} files, 8.4 MB</span>
        </div>
      </div>
    </div>
  )
}

export default FilesPage
