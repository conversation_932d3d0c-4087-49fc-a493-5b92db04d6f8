import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'
import { useAppStore } from '../store'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faFolder,
  faFolderOpen,
  faFileText,
  faFileCode,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileImage,
  faFileVideo,
  faFileAudio,
  faFileZipper,
  faFile,
  faChevronDown,
  faChevronRight,
  faFolderTree,
  faPlus,
  faSitemap,
  faLightbulb,
  faClock,
  faUser,
  faInfoCircle,
  faStar,
  faCheck,
  faRocket,
  faTerminal,
  faCode,
  faPuzzlePiece,
  faFoundation,
  faCubes,
  faArrowLeft,
  faArrowRight,
  faHome,
  faEye,
  faTh,
  faList,
  faEllipsisVertical,
  faFiles,
  faHdd
} from '@fortawesome/free-solid-svg-icons'

interface FileTreeNode {
  type: 'folder' | 'file'
  name: string
  path: string
  isExpanded?: boolean
  isSelected?: boolean
  children?: FileTreeNode[]
  fileCount?: number
  icon: string
  color: string
}

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}

// Helper function to get file type icon and color
const getFileTypeIcon = (fileName: string, fileType: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase()

  if (fileType === 'Folder') {
    return { icon: faFolder, color: 'text-supplement2', bgColor: 'bg-supplement2/20' }
  }

  switch (extension) {
    case 'md':
    case 'markdown':
      return { icon: faFileText, color: 'text-primary', bgColor: 'bg-primary/20' }
    case 'json':
      return { icon: faFileCode, color: 'text-yellow-400', bgColor: 'bg-yellow-500/20' }
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return { icon: faFileCode, color: 'text-blue-400', bgColor: 'bg-blue-500/20' }
    case 'pdf':
      return { icon: faFilePdf, color: 'text-red-400', bgColor: 'bg-red-500/20' }
    case 'doc':
    case 'docx':
      return { icon: faFileWord, color: 'text-blue-600', bgColor: 'bg-blue-600/20' }
    case 'xls':
    case 'xlsx':
      return { icon: faFileExcel, color: 'text-green-400', bgColor: 'bg-green-500/20' }
    case 'ppt':
    case 'pptx':
      return { icon: faFilePowerpoint, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
      return { icon: faFileImage, color: 'text-purple-400', bgColor: 'bg-purple-500/20' }
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return { icon: faFileVideo, color: 'text-pink-400', bgColor: 'bg-pink-500/20' }
    case 'mp3':
    case 'wav':
    case 'flac':
      return { icon: faFileAudio, color: 'text-indigo-400', bgColor: 'bg-indigo-500/20' }
    case 'zip':
    case 'rar':
    case '7z':
      return { icon: faFileZipper, color: 'text-gray-400', bgColor: 'bg-gray-500/20' }
    case 'css':
      return { icon: faFileCode, color: 'text-cyan-400', bgColor: 'bg-cyan-500/20' }
    case 'html':
    case 'htm':
      return { icon: faFileCode, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    default:
      return { icon: faFile, color: 'text-supplement1', bgColor: 'bg-supplement1/20' }
  }
}

const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const { artifactsVisible } = useAppStore()

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])
  
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'master', // Default to master mode as shown in design
    showArtifacts: false,
    artifactsExpanded: false
  })
  
  const [selectedFile, setSelectedFile] = useState<string | null>('master.md')
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root', 'components']))
  const [fileTree] = useState<FileTreeNode[]>([
    {
      type: 'folder',
      name: 'project-alpha',
      path: '/project-alpha',
      isExpanded: true,
      fileCount: 8,
      icon: faFolderOpen,
      color: 'text-supplement2',
      children: [
        {
          type: 'file',
          name: 'master.md',
          path: '/project-alpha/master.md',
          isSelected: true,
          icon: faFileText,
          color: 'text-primary'
        },
        {
          type: 'folder',
          name: 'design',
          path: '/project-alpha/design',
          isExpanded: false,
          fileCount: 2,
          icon: faFolder,
          color: 'text-supplement2',
          children: []
        },
        {
          type: 'folder',
          name: 'components',
          path: '/project-alpha/components',
          isExpanded: true,
          fileCount: 2,
          icon: faFolderOpen,
          color: 'text-supplement2',
          children: [
            {
              type: 'file',
              name: 'buttons.md',
              path: '/project-alpha/components/buttons.md',
              icon: faFileText,
              color: 'text-secondary'
            },
            {
              type: 'file',
              name: 'forms.md',
              path: '/project-alpha/components/forms.md',
              icon: faFileText,
              color: 'text-gray-400'
            }
          ]
        },
        {
          type: 'file',
          name: 'tokens.json',
          path: '/project-alpha/tokens.json',
          icon: faFileCode,
          color: 'text-supplement2'
        },
        {
          type: 'folder',
          name: 'docs',
          path: '/project-alpha/docs',
          isExpanded: false,
          fileCount: 3,
          icon: faFolder,
          color: 'text-supplement2',
          children: []
        },
        {
          type: 'file',
          name: 'README.md',
          path: '/project-alpha/README.md',
          icon: faFileText,
          color: 'text-supplement1'
        }
      ]
    }
  ])

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })
  }

  const selectFile = (path: string) => {
    setSelectedFile(path)
  }

  const handleModeChange = (mode: 'explorer' | 'master') => {
    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))

    // Auto-select master.md when switching to master mode
    if (mode === 'master') {
      setSelectedFile('master.md')
    }
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path)
            } else {
              selectFile(node.path)
            }
          }}
        >
          {node.type === 'folder' && (
            <FontAwesomeIcon
              icon={isExpanded ? faChevronDown : faChevronRight}
              className="text-gray-400 text-xs w-3"
            />
          )}
          {node.type === 'file' && <div className="w-3"></div>}

          <FontAwesomeIcon icon={node.icon} className={`text-sm ${node.color}`} />
          
          <span className={`text-sm ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          
          <div className="ml-auto">
            {node.fileCount && (
              <span className={`w-5 h-5 ${node.color === 'text-secondary' ? 'bg-secondary/20 text-secondary' : 'bg-supplement2/20 text-supplement2'} text-xs rounded-full flex items-center justify-center font-medium`}>
                {node.fileCount}
              </span>
            )}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Main Files Content */}
      <div className="flex-1 flex bg-gray-900">
        
        {/* Left Column - File Tree (20%) */}
        <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
          
          {/* File Tree Header */}
          <div className="p-4 border-b border-tertiary/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faFolderTree} className="text-supplement2 text-sm" />
                <h3 className="font-medium text-supplement1 text-sm">Files in Vault</h3>
              </div>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <FontAwesomeIcon icon={faPlus} className="text-gray-400 text-xs" />
                <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  Add File
                </div>
              </button>
            </div>
          </div>

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faSitemap} className="text-sm" />
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master'
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80'
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faLightbulb} className="text-sm" />
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree */}
          <div className="flex-1 overflow-y-auto p-2">
            {fileTree.map(node => renderFileTreeNode(node))}
          </div>
        </div>
        
        {/* Right Column Content */}
        {viewMode.currentMode === 'master' ? (
          <MasterMode />
        ) : (
          <ExplorerMode />
        )}
      </div>
    </div>
  )
}

// Master Mode Component
const MasterMode: React.FC = () => {
  return (
    <div className="flex-1 flex">
      {/* Master Document Preview */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="markdown-content">
          {/* Document Header */}
          <div className="flex items-center gap-3 mb-6 pb-4 border-b border-tertiary/30">
            <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
              <FontAwesomeIcon icon={faFileText} className="text-primary text-lg" />
            </div>
            <div>
              <h1 className="text-supplement1 text-xl font-semibold">Project Alpha Design System</h1>
              <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                <span className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faClock} className="text-xs" />
                  Modified 2 hours ago
                </span>
                <span className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faFileText} className="text-xs" />
                  12.5 KB
                </span>
                <span className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faUser} className="text-xs" />
                  Design Team
                </span>
              </div>
            </div>
          </div>

          <p className="text-gray-400 mb-6">A comprehensive design system for modern web applications, built with accessibility and scalability in mind.</p>

          <h2 className="text-supplement1 text-lg font-medium mb-3 mt-6 flex items-center gap-2">
            <i className="fa-info-circle text-supplement2 text-sm"></i>
            Overview
          </h2>
          <p className="text-gray-400 mb-4">This design system provides a unified set of components, tokens, and guidelines to ensure consistency across all product interfaces. It includes everything from basic UI elements to complex interaction patterns.</p>

          <h3 className="text-supplement2 text-base font-medium mb-2 mt-4 flex items-center gap-2">
            <i className="fa-star text-supplement2 text-sm"></i>
            Key Features
          </h3>
          <ul className="text-gray-400 ml-6 mb-4 space-y-1">
            <li className="flex items-start gap-2">
              <i className="fa-check text-green-400 text-xs mt-1"></i>
              Comprehensive component library with 50+ components
            </li>
            <li className="flex items-start gap-2">
              <i className="fa-check text-green-400 text-xs mt-1"></i>
              Design tokens for colors, typography, spacing, and shadows
            </li>
            <li className="flex items-start gap-2">
              <i className="fa-check text-green-400 text-xs mt-1"></i>
              Accessibility guidelines and WCAG 2.1 AA compliance
            </li>
            <li className="flex items-start gap-2">
              <i className="fa-check text-green-400 text-xs mt-1"></i>
              Dark and light theme support
            </li>
            <li className="flex items-start gap-2">
              <i className="fa-check text-green-400 text-xs mt-1"></i>
              Responsive design patterns
            </li>
            <li className="flex items-start gap-2">
              <i className="fa-check text-green-400 text-xs mt-1"></i>
              Interactive documentation and examples
            </li>
          </ul>

          <h2 className="text-supplement1 text-lg font-medium mb-3 mt-6 flex items-center gap-2">
            <i className="fa-rocket text-supplement2 text-sm"></i>
            Getting Started
          </h2>
          <p className="text-gray-400 mb-4">To begin using the design system, install the package and import the necessary components:</p>

          <div className="bg-gray-800 rounded-lg p-4 mb-4 border border-tertiary/30">
            <div className="flex items-center gap-2 mb-2">
              <i className="fa-terminal text-supplement2 text-sm"></i>
              <span className="text-xs text-supplement2 font-medium">Terminal</span>
            </div>
            <code className="text-primary text-sm">npm install @company/design-system</code>
          </div>

          <h3 className="text-supplement2 text-base font-medium mb-2 mt-4 flex items-center gap-2">
            <i className="fa-code text-supplement2 text-sm"></i>
            Basic Usage
          </h3>
          <p className="text-gray-400 mb-4">Import components as needed in your application. Each component comes with full TypeScript support and comprehensive documentation.</p>

          <h2 className="text-supplement1 text-lg font-medium mb-3 mt-6 flex items-center gap-2">
            <i className="fa-puzzle-piece text-supplement2 text-sm"></i>
            Component Categories
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div className="bg-gray-800/50 rounded-lg p-4 border border-tertiary/30">
              <h4 className="text-supplement1 font-medium mb-2 flex items-center gap-2">
                <i className="fa-foundation text-primary text-sm"></i>
                Foundation
              </h4>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Colors and themes</li>
                <li>• Typography scales</li>
                <li>• Spacing and layout</li>
                <li>• Icons and illustrations</li>
              </ul>
            </div>

            <div className="bg-gray-800/50 rounded-lg p-4 border border-tertiary/30">
              <h4 className="text-supplement1 font-medium mb-2 flex items-center gap-2">
                <i className="fa-cubes text-secondary text-sm"></i>
                Components
              </h4>
              <ul className="text-gray-400 text-sm space-y-1">
                <li>• Buttons and actions</li>
                <li>• Forms and inputs</li>
                <li>• Navigation elements</li>
                <li>• Data display</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Artifacts Sidebar will be rendered by ArtifactsSidebar component */}
    </div>
  )
}

// Explorer Mode Component
const ExplorerMode: React.FC = () => {
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid')

  const mockFiles = [
    {
      id: '1',
      name: 'master.md',
      type: 'Markdown File',
      size: '12.5 KB',
      modified: '2 hours ago',
      icon: faFileText,
      color: 'text-primary',
      bgColor: 'bg-primary/20'
    },
    {
      id: '2',
      name: 'design',
      type: 'Folder',
      size: '-',
      modified: '5 days ago',
      icon: faFolder,
      color: 'text-supplement2',
      bgColor: 'bg-supplement2/20'
    },
    {
      id: '3',
      name: 'components',
      type: 'Folder',
      size: '-',
      modified: '4 hours ago',
      icon: faFolder,
      color: 'text-supplement2',
      bgColor: 'bg-supplement2/20'
    },
    {
      id: '4',
      name: 'tokens.json',
      type: 'JSON File',
      size: '8.2 KB',
      modified: '3 days ago',
      icon: faFileCode,
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/20'
    },
    {
      id: '5',
      name: 'docs',
      type: 'Folder',
      size: '-',
      modified: '1 week ago',
      icon: faFolder,
      color: 'text-supplement2',
      bgColor: 'bg-supplement2/20'
    },
    {
      id: '6',
      name: 'README.md',
      type: 'Markdown File',
      size: '3.1 KB',
      modified: '2 weeks ago',
      icon: faFileText,
      color: 'text-supplement1',
      bgColor: 'bg-supplement1/20'
    },
    {
      id: '7',
      name: 'package.json',
      type: 'JSON File',
      size: '1.2 KB',
      modified: '1 week ago',
      icon: faFileCode,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20'
    },
    {
      id: '8',
      name: 'style-guide.pdf',
      type: 'PDF Document',
      size: '4.8 MB',
      modified: '3 days ago',
      icon: faFilePdf,
      color: 'text-red-400',
      bgColor: 'bg-red-500/20'
    }
  ]

  return (
    <div className="flex-1 flex flex-col bg-gray-900">

      {/* Breadcrumb Navigation */}
      <div className="p-4 border-b border-tertiary/50 bg-gray-800/50">
        <div className="flex items-center gap-2">
          <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
            <FontAwesomeIcon icon={faArrowLeft} className="text-gray-400 text-sm" />
            <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Back
            </div>
          </button>
          <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
            <FontAwesomeIcon icon={faArrowRight} className="text-gray-400 text-sm" />
            <div className="absolute bottom-6 left-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              Forward
            </div>
          </button>
          <div className="flex items-center gap-1 ml-2">
            <FontAwesomeIcon icon={faHome} className="text-supplement2 text-sm" />
            <span className="text-sm text-supplement1">Project Alpha</span>
            <FontAwesomeIcon icon={faChevronRight} className="text-gray-400 text-xs" />
            <span className="text-sm text-gray-400">Design System</span>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <div className="flex items-center gap-1 mr-3">
              <FontAwesomeIcon icon={faEye} className="text-gray-400 text-xs" />
              <span className="text-xs text-gray-400">Preview</span>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors">
                <FontAwesomeIcon icon={faChevronRight} className="text-gray-400 text-xs" />
              </button>
            </div>
            <button
              onClick={() => setViewType('grid')}
              className={`p-2 hover:bg-gray-700 rounded transition-colors group relative ${viewType === 'grid' ? 'text-primary bg-primary/10' : 'text-gray-400'}`}
            >
              <FontAwesomeIcon icon={faTh} className="text-sm" />
              <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                Grid View
              </div>
            </button>
            <button
              onClick={() => setViewType('list')}
              className={`p-2 hover:bg-gray-700 rounded transition-colors group relative ${viewType === 'list' ? 'text-primary bg-primary/10' : 'text-gray-400'}`}
            >
              <FontAwesomeIcon icon={faList} className="text-sm" />
              <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                List View
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* File Grid/List View */}
      <div className="flex-1 overflow-y-auto p-4">
        {viewType === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {mockFiles.map((file) => {
              const fileIcon = getFileTypeIcon(file.name, file.type)
              return (
                <div
                  key={file.id}
                  className="file-item p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30 group"
                  onClick={() => {
                    if (file.type !== 'Folder') {
                      // Open artifacts for supported file types
                      console.log('Opening file:', file.name)
                    }
                  }}
                >
                  <div className="flex flex-col items-center text-center">
                    <div className={`w-12 h-12 ${fileIcon.bgColor} rounded-lg flex items-center justify-center mb-3 group-hover:scale-105 transition-transform`}>
                      <FontAwesomeIcon icon={fileIcon.icon} className={`${fileIcon.color} text-xl`} />
                    </div>
                    <h4 className="text-sm font-medium text-supplement1 mb-1 truncate w-full">{file.name}</h4>
                    <p className="text-xs text-gray-400">{file.size} • {file.type}</p>
                    <p className="text-xs text-gray-500 mt-1">Modified {file.modified}</p>
                  </div>
                </div>
              )
            })}
          </div>
        ) : (
          <div className="space-y-1">
            {mockFiles.map((file) => {
              const fileIcon = getFileTypeIcon(file.name, file.type)
              return (
                <div
                  key={file.id}
                  className="file-item p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border border-transparent hover:border-primary/30 group"
                  onClick={() => {
                    if (file.type !== 'Folder') {
                      // Open artifacts for supported file types
                      console.log('Opening file:', file.name)
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 ${fileIcon.bgColor} rounded flex items-center justify-center group-hover:scale-105 transition-transform`}>
                      <FontAwesomeIcon icon={fileIcon.icon} className={`${fileIcon.color} text-sm`} />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-supplement1 truncate">{file.name}</h4>
                      <div className="flex items-center gap-2 text-xs text-gray-400">
                        <span>{file.size}</span>
                        <span>•</span>
                        <span>{file.type}</span>
                        <span>•</span>
                        <span className="flex items-center gap-1">
                          <i className="fa-clock text-xs"></i>
                          {file.modified}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button className="p-1 hover:bg-gray-600 rounded transition-colors opacity-0 group-hover:opacity-100">
                        <i className="fa-ellipsis-vertical text-gray-400 text-xs"></i>
                      </button>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="p-3 border-t border-tertiary/50 bg-gray-800/50">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <FontAwesomeIcon icon={faFiles} className="text-xs" />
              {mockFiles.length} items
            </span>
            <span className="flex items-center gap-1">
              <FontAwesomeIcon icon={faFolder} className="text-xs" />
              {mockFiles.filter(f => f.type === 'Folder').length} folders
            </span>
            <span className="flex items-center gap-1">
              <FontAwesomeIcon icon={faFile} className="text-xs" />
              {mockFiles.filter(f => f.type !== 'Folder').length} files
            </span>
          </div>
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <FontAwesomeIcon icon={faHdd} className="text-xs" />
              Total: 8.4 MB
            </span>
            <span className="flex items-center gap-1">
              <FontAwesomeIcon icon={faClock} className="text-xs" />
              Last modified: 2h ago
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FilesPage
